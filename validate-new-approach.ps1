#!/usr/bin/env pwsh
# Validation script for New Approach GitOps Changes

Write-Host "🚀 Validating New Approach GitOps Configuration" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Function to check if kubectl is available
function Test-KubectlAvailable {
    try {
        kubectl version --client --short | Out-Null
        return $true
    }
    catch {
        Write-Host "❌ kubectl is not available or not configured" -ForegroundColor Red
        return $false
    }
}

# Function to validate YAML syntax
function Test-YamlSyntax {
    param([string]$FilePath)
    
    Write-Host "🔍 Validating YAML syntax: $FilePath" -ForegroundColor Yellow
    
    if (Test-Path $FilePath) {
        try {
            # Use kubectl to validate YAML syntax
            kubectl apply --dry-run=client -f $FilePath | Out-Null
            Write-Host "✅ YAML syntax valid: $FilePath" -ForegroundColor Green
            return $true
        }
        catch {
            Write-Host "❌ YAML syntax error in: $FilePath" -ForegroundColor Red
            Write-Host $_.Exception.Message -ForegroundColor Red
            return $false
        }
    }
    else {
        Write-Host "❌ File not found: $FilePath" -ForegroundColor Red
        return $false
    }
}

# Function to check ConfigMap content
function Test-ConfigMapContent {
    Write-Host "🔍 Validating ConfigMap content..." -ForegroundColor Yellow
    
    $configMapPath = "ai-react-frontend/k8s/configmap.yaml"
    
    if (Test-Path $configMapPath) {
        $content = Get-Content $configMapPath -Raw
        
        # Check for env-config.js
        if ($content -match "env-config\.js:") {
            Write-Host "✅ env-config.js found in ConfigMap" -ForegroundColor Green
        }
        else {
            Write-Host "❌ env-config.js not found in ConfigMap" -ForegroundColor Red
            return $false
        }
        
        # Check for window._env_
        if ($content -match "window\._env_") {
            Write-Host "✅ window._env_ configuration found" -ForegroundColor Green
        }
        else {
            Write-Host "❌ window._env_ configuration not found" -ForegroundColor Red
            return $false
        }
        
        # Check for required environment variables
        $requiredVars = @(
            "REACT_APP_BACKEND_URL",
            "REACT_APP_CURRENT_BACKEND",
            "REACT_APP_ENVIRONMENT",
            "REACT_APP_API_VERSION"
        )
        
        foreach ($var in $requiredVars) {
            if ($content -match $var) {
                Write-Host "✅ Required variable found: $var" -ForegroundColor Green
            }
            else {
                Write-Host "❌ Required variable missing: $var" -ForegroundColor Red
                return $false
            }
        }
        
        return $true
    }
    else {
        Write-Host "❌ ConfigMap file not found" -ForegroundColor Red
        return $false
    }
}

# Function to check Deployment configuration
function Test-DeploymentConfig {
    Write-Host "🔍 Validating Deployment configuration..." -ForegroundColor Yellow
    
    $deploymentPath = "ai-react-frontend/k8s/deployment.yaml"
    
    if (Test-Path $deploymentPath) {
        $content = Get-Content $deploymentPath -Raw
        
        # Check for env-config volume mount
        if ($content -match "env-config-volume") {
            Write-Host "✅ env-config-volume found in deployment" -ForegroundColor Green
        }
        else {
            Write-Host "❌ env-config-volume not found in deployment" -ForegroundColor Red
            return $false
        }
        
        # Check for correct mount path
        if ($content -match "/usr/share/nginx/html/env-config\.js") {
            Write-Host "✅ Correct mount path for env-config.js" -ForegroundColor Green
        }
        else {
            Write-Host "❌ Incorrect mount path for env-config.js" -ForegroundColor Red
            return $false
        }
        
        # Check for port 3000
        if ($content -match "containerPort: 3000") {
            Write-Host "✅ Container port set to 3000" -ForegroundColor Green
        }
        else {
            Write-Host "❌ Container port not set to 3000" -ForegroundColor Red
            return $false
        }
        
        return $true
    }
    else {
        Write-Host "❌ Deployment file not found" -ForegroundColor Red
        return $false
    }
}

# Function to check Nginx configuration
function Test-NginxConfig {
    Write-Host "🔍 Validating Nginx configuration..." -ForegroundColor Yellow
    
    $nginxPath = "ai-react-frontend/k8s/nginx-configmap.yaml"
    
    if (Test-Path $nginxPath) {
        $content = Get-Content $nginxPath -Raw
        
        # Check for env-config.js location block
        if ($content -match "location /env-config\.js") {
            Write-Host "✅ env-config.js location block found" -ForegroundColor Green
        }
        else {
            Write-Host "❌ env-config.js location block not found" -ForegroundColor Red
            return $false
        }
        
        # Check for no-cache headers
        if ($content -match "no-cache, no-store, must-revalidate") {
            Write-Host "✅ No-cache headers configured for env-config.js" -ForegroundColor Green
        }
        else {
            Write-Host "❌ No-cache headers not configured" -ForegroundColor Red
            return $false
        }
        
        # Check that hardcoded backend forcing is removed
        if ($content -match "FORCE.*BACKEND" -or $content -match "proxy_pass.*64\.225\.86\.51") {
            Write-Host "❌ Hardcoded backend forcing still present" -ForegroundColor Red
            return $false
        }
        else {
            Write-Host "✅ Hardcoded backend forcing removed" -ForegroundColor Green
        }
        
        # Check for port 3000
        if ($content -match "listen 3000") {
            Write-Host "✅ Nginx listening on port 3000" -ForegroundColor Green
        }
        else {
            Write-Host "❌ Nginx not listening on port 3000" -ForegroundColor Red
            return $false
        }
        
        return $true
    }
    else {
        Write-Host "❌ Nginx ConfigMap file not found" -ForegroundColor Red
        return $false
    }
}

# Function to check backend switching scripts
function Test-BackendSwitchScripts {
    Write-Host "🔍 Validating backend switching scripts..." -ForegroundColor Yellow
    
    $scriptsPath = "ai-react-frontend/k8s/backend-switch-scripts.yaml"
    
    if (Test-Path $scriptsPath) {
        $content = Get-Content $scriptsPath -Raw
        
        # Check for updated scripts using env-config.js
        if ($content -match "ai-react-frontend-env") {
            Write-Host "✅ Scripts updated to use ai-react-frontend-env ConfigMap" -ForegroundColor Green
        }
        else {
            Write-Host "❌ Scripts not updated to use new ConfigMap" -ForegroundColor Red
            return $false
        }
        
        # Check for window._env_ in scripts
        if ($content -match "window\._env_") {
            Write-Host "✅ Scripts use window._env_ format" -ForegroundColor Green
        }
        else {
            Write-Host "❌ Scripts don't use window._env_ format" -ForegroundColor Red
            return $false
        }
        
        return $true
    }
    else {
        Write-Host "❌ Backend switch scripts file not found" -ForegroundColor Red
        return $false
    }
}

# Main validation
Write-Host "`n🔧 Starting validation..." -ForegroundColor Cyan

$allValid = $true

# Check kubectl availability
if (-not (Test-KubectlAvailable)) {
    Write-Host "⚠️  kubectl not available - skipping cluster validation" -ForegroundColor Yellow
}

# Validate YAML files
$yamlFiles = @(
    "ai-react-frontend/k8s/configmap.yaml",
    "ai-react-frontend/k8s/deployment.yaml", 
    "ai-react-frontend/k8s/nginx-configmap.yaml",
    "ai-react-frontend/k8s/backend-switch-scripts.yaml"
)

foreach ($file in $yamlFiles) {
    if (-not (Test-YamlSyntax $file)) {
        $allValid = $false
    }
}

# Validate content
if (-not (Test-ConfigMapContent)) { $allValid = $false }
if (-not (Test-DeploymentConfig)) { $allValid = $false }
if (-not (Test-NginxConfig)) { $allValid = $false }
if (-not (Test-BackendSwitchScripts)) { $allValid = $false }

# Summary
Write-Host "`n📋 Validation Summary" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

if ($allValid) {
    Write-Host "✅ All validations passed! New approach configuration is ready." -ForegroundColor Green
    Write-Host "`n🚀 Next steps:" -ForegroundColor Yellow
    Write-Host "1. Apply the updated manifests to your cluster" -ForegroundColor White
    Write-Host "2. Test env-config.js accessibility" -ForegroundColor White
    Write-Host "3. Test backend switching using the updated scripts" -ForegroundColor White
}
else {
    Write-Host "❌ Some validations failed. Please fix the issues above." -ForegroundColor Red
    exit 1
}

Write-Host "`n🎉 Validation completed!" -ForegroundColor Green
