apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: dev-overlay
  annotations:
    config.kubernetes.io/local-config: "true"

# Reference to base configuration
resources:
- ../../../templates/k8s/base

# Environment-specific namespace
namespace: ai-spring-backend-dev

# Environment-specific name prefix
namePrefix: ""

# Common labels for dev environment
commonLabels:
  environment: dev
  deployment.tier: dev

# Common annotations for dev environment
commonAnnotations:
  argocd.argoproj.io/sync-options: "CreateNamespace=true"
  deployment.environment: "dev"

# Dev-specific image configuration
images:
- name: test/spring-app
  newTag: latest

# ConfigMap generator for environment-specific variables
configMapGenerator:
- name: kustomize-vars
  literals:
  - API_URL=http://ai-spring-backend-service:8080
  - APPLICATION_TYPE=springboot-backend
  - APP_NAME=Test Spring Backend
  - APP_TYPE=springboot-backend
  - APP_URL=http://ai-spring-backend.dev.local
  - APP_VERSION=1.0.0
  - CLUSTER_ID=default
  - CLUSTER_NAME=in-cluster
  - CLUSTER_SERVER=https://kubernetes.default.svc
  - CONTAINER_IMAGE=test/spring-app:latest
  - CONTAINER_PORT=8080
  - CORS_ORIGINS=http://localhost:3000,http://localhost:3001
  - CPU_LIMIT=500m
  - CPU_REQUEST=100m
  - DB_HOST=*************
  - DB_NAME=ai_spring_backend
  - DB_PASSWORD=password
  - DB_PASSWORD_B64=cGFzc3dvcmQ=
  - DB_PORT=5432
  - DB_USER=postgres
  - DB_USER_B64=cG9zdGdyZXM=
  - DOCKER_IMAGE=test/spring-app
  - DOCKER_TAG=latest
  - ENABLE_DATABASE=false
  - ENABLE_INGRESS=false
  - ENABLE_PVC=false
  - ENVIRONMENT=dev
  - GOOGLE_CLIENT_ID=1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com
  - GOOGLE_CLIENT_ID_B64=MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==
  - GOOGLE_CLIENT_SECRET=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
  - GOOGLE_CLIENT_SECRET_B64=R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=
  - GOOGLE_REDIRECT_URI=http://ai-spring-backend.dev.local/auth/google/callback
  - HEALTH_CHECK_PATH=/health
  - INGRESS_PATH=/
  - JAVA_XMS=256m
  - JAVA_XMX=512m
  - JWT_EXPIRATION=24h
  - JWT_SECRET=supersecretkey
  - JWT_SECRET_B64=c3VwZXJzZWNyZXRrZXk=
  - MEMORY_LIMIT=512Mi
  - MEMORY_REQUEST=256Mi
  - NAMESPACE=ai-spring-backend-dev
  - OAUTH_REDIRECT_URIS=http://ai-spring-backend.dev.local/auth/google/callback
  - OAUTH_SCOPES=openid,profile,email
  - PROJECT_ID=ai-spring-backend
  - PUBLIC_URL=http://ai-spring-backend.dev.local
  - PVC_SIZE=1Gi
  - REPLICAS=1
  - SERVICE_NAME=ai-spring-backend-service
  - SERVICE_NAMESPACE=ai-spring-backend-dev
  - SERVICE_PORT=8080
  - SMTP_FROM=<EMAIL>
  - SMTP_HOST=smtp.gmail.com
  - SMTP_PASS=fqactehafmzlltzz
  - SMTP_PASS_B64=ZnFhY3RlaGFmbXpsbHR6eg==
  - SMTP_PORT=587
  - SMTP_USER=<EMAIL>
  - SMTP_USER_B64=****************************************
  - STORAGE_SIZE=5Gi
  - SERVICE_NAME=ai-spring-backend-service
  - SERVICE_NAMESPACE=ai-spring-backend-dev
  - SERVICE_PORT=8080
  - SERVICE_TYPE=springboot-backend
  - SERVICE_HEALTH_PATH=/health

# Environment-specific patches
patches:
- path: deployment-patch.yaml
  target:
    kind: Deployment
    name: ai-spring-backend
- path: resource-patch.yaml
  target:
    kind: ResourceQuota
    name: ai-spring-backend-resource-quota
- path: security-patch.yaml
  target:
    kind: Deployment
    name: ai-spring-backend

# Remove HPA in development (disabled)
patchesJson6902:
- target:
    version: v2
    kind: HorizontalPodAutoscaler
    name: ai-spring-backend-hpa
  path: disable-hpa-patch.yaml

# Remove Network Policy in development (disabled)
- target:
    version: v1
    kind: NetworkPolicy
    name: ai-spring-backend-network-policy
  path: disable-network-policy-patch.yaml